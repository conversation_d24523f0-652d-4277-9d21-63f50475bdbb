{"name": "iranian-community-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy": "npm run build && firebase deploy", "deploy:hosting": "npm run build && firebase deploy --only hosting", "deploy:functions": "firebase deploy --only functions", "deploy:rules": "firebase deploy --only firestore:rules,storage"}, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.3.0", "@mui/x-data-grid": "^7.23.2", "@mui/x-date-pickers": "^7.23.2", "@reduxjs/toolkit": "^2.5.0", "axios": "^1.7.9", "chart.js": "^4.4.7", "dayjs": "^1.11.13", "firebase": "^11.8.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "recharts": "^2.15.3", "yup": "^1.6.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}